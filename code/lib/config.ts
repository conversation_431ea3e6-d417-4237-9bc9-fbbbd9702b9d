import dayjs from "dayjs";

// 固定的微信模板配置
// export const WECHAT_TEMPLATE = {
//   templateId: "nqfLLYWb80sT08ovTRI0Ozv1WBXhFz_lBYPzA7_RpdQ",
//   templateNumber: "43635",
//   title: "租金逾期提醒",
//   category: "租车",
//   description: "提醒客户缴纳租金",
//   content: {
//     thing12: "项目名称",
//     time6: "还款日期",
//     amount3: "应付租金",
//   },
//   // 默认提前提醒天数
//   defaultAdvanceDays: 3,
// };
// 固定的微信模板配置
export const WECHAT_TEMPLATE = {
  templateId: "QqgtNhUWYG8EsxArAfAJN-ISRDj5ak5URR7ZPAuymCM",
  templateNumber: "45931",
  title: "客户还款提醒",
  category: "记账",
  description: "还款提醒",
  content: {
    thing14: "还款项目",
    amount3: "还款金额",
    time4: "还款时间",
  },
  // 默认提前提醒天数
  defaultAdvanceDays: 3,
};

// 生成场景字符串
export function generateSceneStr(projectName: string, paymentDay: number, amount: string): string {
  // 使用时间戳作为前缀，再加上项目名称、还款日和金额的组合
  const timestamp = Date.now().toString();
  return `${timestamp}_${projectName.slice(0, 8)}_${paymentDay}_${amount}`;
}

// 生成二维码名称
export function generateQRCodeName(projectName: string, paymentDay: number, amount: string): string {
  return `${projectName}-${paymentDay}日-${amount}元`;
}

// 获取模板字段名称的辅助函数
export function getTemplateFieldNames() {
  const fields = Object.keys(WECHAT_TEMPLATE.content);
  return {
    projectField: fields[0], // 第一个字段是项目名称
    amountField: fields[1],  // 第二个字段是金额
    timeField: fields[2],    // 第三个字段是时间
  };
}

// 获取模板字段的中文显示名称
export function getTemplateFieldLabels() {
  return WECHAT_TEMPLATE.content;
}

// 解析模板数据的辅助函数
export function parseTemplateData(templateDataStr: string) {
  try {
    const templateData = JSON.parse(templateDataStr);
    const { projectField, amountField, timeField } = getTemplateFieldNames();

    return {
      projectName: templateData[projectField]?.value || "项目",
      amount: templateData[amountField]?.value || "0",
      paymentDate: templateData[timeField]?.value || "",
    };
  } catch (e) {
    console.error("解析模板数据失败", e);
    return {
      projectName: "项目",
      amount: "0",
      paymentDate: "",
    };
  }
}

// 生成模板数据
export function generateTemplateData(projectName: string, paymentDate: string, amount: string) {
  const { projectField, amountField, timeField } = getTemplateFieldNames();

  return {
    [projectField]: {
      value: projectName,
    },
    [amountField]: {
      value: amount,
    },
    [timeField]: {
      value: paymentDate,
    },
  };
}

// 格式化日期为 YYYY-MM-DD 格式
export function formatDate(date: Date): string {
  return date.toISOString().split("T")[0];
}

// 格式化日期为 YYYY年M月D日 格式
export function formatChineseDate(date: Date): string {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${year}年${month}月${day}日`;
}

// 获取真实的还款日期
export function getRealPaymentDate(paymentDay: number, offsetMonth: number = 0): Date {
  const today = new Date();
  //今天要使用0点
  today.setHours(0, 0, 0, 0);
  const jsDay = dayjs(today);
  // 目标月的jsDay
  const targetMonthJsDay = jsDay.add(offsetMonth, "month");
  return targetMonthJsDay.date(paymentDay).toDate();
}

// 计算下一个还款日期
export function getNextPaymentDate(paymentDay: number): Date {
  const today = new Date();

  // 获取当月的还款日期
  const currentMonthPayment = getRealPaymentDate(paymentDay, 0);

  // 如果当月的还款日已经过了，使用下个月的还款日
  if (today > currentMonthPayment) {
    return getRealPaymentDate(paymentDay, 1);
  }

  return currentMonthPayment;
}

// 计算提醒日期（还款日前N天）
export function calculateReminderDate(
  paymentDay: number,
  advanceDays: number = WECHAT_TEMPLATE.defaultAdvanceDays
): Date {
  // 获取下一个还款日期
  const paymentDate = getNextPaymentDate(paymentDay);

  //使用dayjs计算
  // 计算提醒日期（还款日前N天）
  const reminderDate = dayjs(paymentDate).subtract(advanceDays, "day").toDate();

  return reminderDate;
}

// 计算未来几个月的还款日期
export function calculateFuturePaymentDates(paymentDay: number, months: number): Date[] {
  const dates: Date[] = [];

  // 获取下一个还款日期
  const nextPaymentDate = getNextPaymentDate(paymentDay);
  //判断 nextPaymentDate是不是这个月
  const isThisMonth = nextPaymentDate.getMonth() === new Date().getMonth();

  // 计算后续几个月的还款日期
  for (let i = 0; i < months; i++) {
    // 获取当前月份后i个月的还款日期
    const paymentDate = getRealPaymentDate(paymentDay, i + (isThisMonth ? 0 : 1));
    dates.push(paymentDate);
  }

  return dates;
}

// 计算未来几个月的提醒日期（还款日前N天）
export function calculateFutureReminderDates(
  paymentDay: number,
  months: number,
  advanceDays: number = WECHAT_TEMPLATE.defaultAdvanceDays
): Date[] {
  // 先获取未来几个月的还款日期
  const paymentDates = calculateFuturePaymentDates(paymentDay, months);

  // 然后计算每个还款日前N天的提醒日期
  return paymentDates.map((paymentDate) => {
    const reminderDate = new Date(paymentDate);
    reminderDate.setDate(paymentDate.getDate() - advanceDays);
    return reminderDate;
  });
}

// 生成订阅者的通知计划
export function generateNotificationSchedule(
  firstNotifyDate: Date,
  monthlyNotifyDay: number,
  endDate?: Date | null,
  months: number = 12 // 默认生成12个月的计划
): Date[] {
  const schedule: Date[] = [];
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 添加首次通知日期（如果还未过期）
  if (firstNotifyDate >= today) {
    schedule.push(new Date(firstNotifyDate));
  }

  // 生成每月的通知日期
  const startDate = firstNotifyDate > today ? firstNotifyDate : today;
  const startYear = startDate.getFullYear();
  const startMonth = startDate.getMonth();

  for (let i = 0; i < months; i++) {
    const notifyDate = new Date(startYear, startMonth + i, monthlyNotifyDay);

    // 跳过已经过去的日期
    if (notifyDate < today) continue;

    // 如果设置了结束日期，跳过超出结束日期的通知
    if (endDate && notifyDate > endDate) break;

    // 避免重复添加首次通知日期
    if (!schedule.some(date =>
      date.getFullYear() === notifyDate.getFullYear() &&
      date.getMonth() === notifyDate.getMonth() &&
      date.getDate() === notifyDate.getDate()
    )) {
      schedule.push(new Date(notifyDate));
    }
  }

  return schedule.sort((a, b) => a.getTime() - b.getTime());
}

// 本地存储键名
export const STORAGE_KEYS = {
  LAST_MONTHS_COUNT: "lastMonthsCount",
  LAST_ADVANCE_DAYS: "lastAdvanceDays",
  LAST_PROJECT_NAME: "lastProjectName",
  LAST_AMOUNT: "lastAmount",
};
